{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg %}

<div class="container mx-auto">
  <!-- Breadcrumb -->
  <section class="p-5 mt-5">
    <div class="flex flex-wrap items-center -m-2">
      <div class="w-full md:w-1/2 p-2">
        <ul class="flex flex-wrap items-center gap-x-3 mb-2">
          <li>{{ breadcrumb_line_svg | safe }}</li>
          <li>
            <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/">
              <p>Home</p>
            </a>
          </li>
          <li>{{ breadcrumb_line_svg | safe }}</li>
          <li>
            <a class="flex font-medium text-xs text-coolGray-500 dark:text-gray-300 hover:text-coolGray-700" href="/changepassword">Change Password</a>
          </li>
          <li>
            <svg width="6" height="15" viewBox="0 0 6 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5.34 0.671999L2.076 14.1H0.732L3.984 0.671999H5.34Z" fill="#BBC3CF"></path>
            </svg>
          </li>
        </ul>
      </div>
    </div>
  </section>

  <section class="py-4">
    <div class="container px-4 mx-auto">
      <div class="relative py-11 px-16 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
        <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="">
        <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="">
        <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="">
        <div class="relative z-20 flex flex-wrap items-center -m-3">
          <div class="w-full md:w-1/2 p-3">
            <h2 class="mb-6 text-4xl font-bold text-white tracking-tighter">Change/Set your Password</h2>
            <p class="font-normal text-coolGray-200 dark:text-white">Change or Set your BasicSwap / Wallets password.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  {% include 'inc_messages.html' %}

  {% set disabled_coins = [] %}
  {% for c in chains_formatted %}
    {% if c.connection_type == "none" %}
      {% set _ = disabled_coins.append(c.display_name) %}
    {% endif %}
  {% endfor %}

  {% if disabled_coins|length > 0 %}
  <section class="py-4">
    <div class="container px-4 mx-auto">
      <div class="bg-red-50 border border-red-400 rounded-lg p-4 dark:bg-red-900 dark:border-red-600" role="alert">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-400">
              ⚠️ Password Change Blocked - Disabled Coins Detected
            </h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              <p class="mb-2">
                <strong>Changing your password now will break your installation!</strong>
              </p>
              <p class="mb-2">
                The following coins are currently disabled and will NOT have their passwords updated:
              </p>
              <ul class="list-disc list-inside mb-2 font-medium">
                {% for coin_name in disabled_coins %}
                <li>{{ coin_name }}</li>
                {% endfor %}
              </ul>
              <p class="mb-2">
                <strong>What this means:</strong> When you re-enable these coins later, they will still have the old password while your other coins have the new password, causing authentication failures.
              </p>
              <p>
                <strong>Solution:</strong> Please <a href="/settings" class="underline font-medium">enable all coins</a> before changing your password, or wait until all coins are enabled.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  {% endif %}

  <section>
    <div class="pl-6 pr-6 pt-0 pb-0 h-full overflow-hidden">
      <div class="border-coolGray-100">
        <div class="flex flex-wrap items-center justify-between -m-2">
          <div class="w-full pt-2">
            <div class="container mt-5 mx-auto">
              <div class="pt-6 pb-8 bg-coolGray-100 dark:bg-gray-500 rounded-xl">
                <div class="px-6">
                  <form method="post" autocomplete="off" id="change-password-form" {% if disabled_coins|length > 0 %}class="form-disabled"{% endif %}>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      <div class="space-y-6">
                        <div>
                          <label for="oldpassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Current Password
                          </label>
                          <div class="relative">
                            <input
                              type="password"
                              id="oldpassword"
                              name="oldpassword"
                              class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0"
                              placeholder="Enter your current password"
                              {% if disabled_coins|length > 0 %}disabled{% endif %}
                              required
                            />
                            <button
                              type="button"
                              id="toggle-old-password"
                              class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                              aria-label="Toggle password visibility"
                            >
                              <svg id="eye-open-old" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                              <svg id="eye-closed-old" class="w-5 h-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div>
                          <label for="newpassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            New Password
                          </label>
                          <div class="relative">
                            <input
                              type="password"
                              id="newpassword"
                              name="newpassword"
                              class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0"
                              placeholder="Enter your new password"
                              {% if disabled_coins|length > 0 %}disabled{% endif %}
                              required
                            />
                            <button
                              type="button"
                              id="toggle-new-password"
                              class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                              aria-label="Toggle password visibility"
                            >
                              <svg id="eye-open-new" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                              <svg id="eye-closed-new" class="w-5 h-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                              </svg>
                            </button>
                          </div>

                          <div id="caps-warning-new" class="hidden mt-2 text-sm text-amber-700 dark:text-amber-300 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Caps Lock is on
                          </div>

                          <div class="mt-3">
                            <div class="flex items-center justify-between mb-2">
                              <span class="text-sm text-gray-600 dark:text-gray-400">Password Strength:</span>
                              <span id="strength-text" class="text-sm font-medium text-gray-500 dark:text-gray-400">Enter password</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                              <div id="strength-bar" class="h-2 rounded-full transition-all duration-300 bg-gray-300 dark:bg-gray-500" style="width: 0%"></div>
                            </div>
                          </div>
                        </div>

                        <div>
                          <label for="confirmpassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Confirm New Password
                          </label>
                          <div class="relative">
                            <input
                              type="password"
                              id="confirmpassword"
                              name="confirmpassword"
                              class="hover:border-blue-500 bg-gray-50 text-gray-900 appearance-none pr-10 dark:bg-gray-500 dark:text-white border border-gray-300 dark:border-gray-400 dark:text-gray-50 dark:placeholder-gray-400 text-sm rounded-lg outline-none focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 focus:ring-0"
                              placeholder="Confirm your new password"
                              {% if disabled_coins|length > 0 %}disabled{% endif %}
                              required
                            />
                            <button
                              type="button"
                              id="toggle-confirm-password"
                              class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                              aria-label="Toggle password visibility"
                            >
                              <svg id="eye-open-confirm" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                              </svg>
                              <svg id="eye-closed-confirm" class="w-5 h-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                              </svg>
                            </button>
                          </div>

                          <div id="password-match" class="mt-2 text-sm hidden">
                            <div id="match-success" class="text-green-600 dark:text-green-400 flex items-center hidden">
                              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                              </svg>
                              Passwords match
                            </div>
                            <div id="match-error" class="text-red-600 dark:text-red-400 flex items-center hidden">
                              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L10 10.586l1.293-1.293a1 1 0 001.414 1.414L10 13.414l-1.293-1.293a1 1 0 00-1.414-1.414z" clip-rule="evenodd"></path>
                              </svg>
                              Passwords do not match
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="space-y-6">
                        <div class="bg-gray-50 dark:bg-gray-600 rounded-lg p-6">
                          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Password Requirements</h3>
                          <div class="space-y-3">
                            <div id="req-length" class="flex items-center text-gray-500 dark:text-gray-400">
                              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                              </svg>
                              At least 8 characters
                            </div>
                            <div id="req-uppercase" class="flex items-center text-gray-500 dark:text-gray-400">
                              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                              </svg>
                              Uppercase letter (A-Z)
                            </div>
                            <div id="req-lowercase" class="flex items-center text-gray-500 dark:text-gray-400">
                              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                              </svg>
                              Lowercase letter (a-z)
                            </div>
                            <div id="req-number" class="flex items-center text-gray-500 dark:text-gray-400">
                              <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                              </svg>
                              Number (0-9)
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mt-8 flex justify-end">
                      <button
                        type="submit"
                        id="submit-btn"
                        class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-8 rounded-lg transition-colors focus:outline-none disabled:cursor-not-allowed"
                        {% if disabled_coins|length > 0 %}disabled{% endif %}
                      >
                        <span id="submit-text">{% if disabled_coins|length > 0 %}Disabled - Enable All Coins First{% else %}Change Password{% endif %}</span>
                        <svg id="submit-spinner" class="hidden animate-spin ml-2 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </button>
                    </div>

                    <input type="hidden" name="formid" value="{{ form_id }}">
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
</section>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const oldPasswordInput = document.getElementById('oldpassword');
      const newPasswordInput = document.getElementById('newpassword');
      const confirmPasswordInput = document.getElementById('confirmpassword');
      const form = document.getElementById('change-password-form');
      const submitBtn = document.getElementById('submit-btn');
      const submitText = document.getElementById('submit-text');
      const submitSpinner = document.getElementById('submit-spinner');

      setupPasswordToggle('old');
      setupPasswordToggle('new');
      setupPasswordToggle('confirm');

      function setupPasswordToggle(type) {
        const toggleBtn = document.getElementById(`toggle-${type}-password`);
        const passwordInput = document.getElementById(`${type}password`);
        const eyeOpen = document.getElementById(`eye-open-${type}`);
        const eyeClosed = document.getElementById(`eye-closed-${type}`);

        if (toggleBtn && passwordInput) {
          toggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isPassword = passwordInput.type === 'password';
            const cursorPosition = passwordInput.selectionStart;
            const inputValue = passwordInput.value;

            passwordInput.type = isPassword ? 'text' : 'password';
            passwordInput.value = inputValue;

            setTimeout(() => {
              passwordInput.setSelectionRange(cursorPosition, cursorPosition);
            }, 0);

            if (isPassword) {
              eyeOpen.classList.add('hidden');
              eyeClosed.classList.remove('hidden');
            } else {
              eyeOpen.classList.remove('hidden');
              eyeClosed.classList.add('hidden');
            }
          });

          toggleBtn.addEventListener('mousedown', function(e) {
            e.preventDefault();
          });
        }
      }

      if (newPasswordInput) {
        const capsWarning = document.getElementById('caps-warning-new');

        newPasswordInput.addEventListener('keydown', function(e) {
          const capsLockOn = e.getModifierState && e.getModifierState('CapsLock');
          if (capsLockOn && capsWarning) {
            capsWarning.classList.remove('hidden');
          } else if (capsWarning) {
            capsWarning.classList.add('hidden');
          }
        });

        newPasswordInput.addEventListener('keyup', function(e) {
          const capsLockOn = e.getModifierState && e.getModifierState('CapsLock');
          if (!capsLockOn && capsWarning) {
            capsWarning.classList.add('hidden');
          }
        });
      }

      function calculatePasswordStrength(password) {
        let score = 0;
        const requirements = {
          length: password.length >= 8,
          uppercase: /[A-Z]/.test(password),
          lowercase: /[a-z]/.test(password),
          number: /\d/.test(password)
        };

        if (requirements.length) score += 25;
        if (requirements.uppercase) score += 25;
        if (requirements.lowercase) score += 25;
        if (requirements.number) score += 25;

        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 10;

        if (password.length >= 12) score += 10;

        return { score: Math.min(score, 100), requirements };
      }

      function updatePasswordStrength(password) {
        const { score, requirements } = calculatePasswordStrength(password);
        const strengthBar = document.getElementById('strength-bar');
        const strengthText = document.getElementById('strength-text');

        if (strengthBar) {
          strengthBar.style.width = `${score}%`;

          if (score === 0) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-gray-300 dark:bg-gray-500';
            strengthText.textContent = 'Enter password';
            strengthText.className = 'text-sm font-medium text-gray-500 dark:text-gray-400';
          } else if (score < 40) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-red-500';
            strengthText.textContent = 'Weak';
            strengthText.className = 'text-sm font-medium text-red-600 dark:text-red-400';
          } else if (score < 70) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-yellow-500';
            strengthText.textContent = 'Fair';
            strengthText.className = 'text-sm font-medium text-yellow-600 dark:text-yellow-400';
          } else if (score < 90) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-blue-500';
            strengthText.textContent = 'Good';
            strengthText.className = 'text-sm font-medium text-blue-600 dark:text-blue-400';
          } else {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-green-500';
            strengthText.textContent = 'Strong';
            strengthText.className = 'text-sm font-medium text-green-600 dark:text-green-400';
          }
        }

        updateRequirement('length', requirements.length);
        updateRequirement('uppercase', requirements.uppercase);
        updateRequirement('lowercase', requirements.lowercase);
        updateRequirement('number', requirements.number);

        return score >= 60;
      }

      function updateRequirement(type, met) {
        const element = document.getElementById(`req-${type}`);
        if (element) {
          if (met) {
            element.className = 'flex items-center text-green-600 dark:text-green-400';
          } else {
            element.className = 'flex items-center text-gray-500 dark:text-gray-400';
          }
        }
      }

      function checkPasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        const matchContainer = document.getElementById('password-match');
        const matchSuccess = document.getElementById('match-success');
        const matchError = document.getElementById('match-error');

        if (confirmPassword.length === 0) {
          matchContainer.classList.add('hidden');
          return false;
        }

        matchContainer.classList.remove('hidden');

        if (newPassword === confirmPassword) {
          matchSuccess.classList.remove('hidden');
          matchError.classList.add('hidden');
          return true;
        } else {
          matchSuccess.classList.add('hidden');
          matchError.classList.remove('hidden');
          return false;
        }
      }

      if (newPasswordInput) {
        newPasswordInput.addEventListener('input', function() {
          updatePasswordStrength(this.value);
          if (confirmPasswordInput.value) {
            checkPasswordMatch();
          }
        });
      }

      if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', checkPasswordMatch);
      }

      if (form) {
        form.addEventListener('submit', function(e) {
          // Check if form is disabled due to disabled coins
          if (form.classList.contains('form-disabled')) {
            e.preventDefault();
            alert('Cannot change password while coins are disabled. Please enable all coins first.');
            return;
          }

          const newPassword = newPasswordInput.value;
          const isStrongEnough = updatePasswordStrength(newPassword);
          const passwordsMatch = checkPasswordMatch();

          if (!isStrongEnough) {
            e.preventDefault();
            alert('Please choose a stronger password.');
            return;
          }

          if (!passwordsMatch) {
            e.preventDefault();
            alert('Passwords do not match.');
            return;
          }

          if (submitBtn && submitText && submitSpinner) {
            submitBtn.disabled = true;
            submitText.textContent = 'Changing Password...';
            submitSpinner.classList.remove('hidden');
          }
        });
      }
    });
  </script>

  <style>
    .form-disabled {
      opacity: 0.6;
      pointer-events: none;
    }
    .form-disabled input[disabled] {
      background-color: #f3f4f6 !important;
      color: #9ca3af !important;
      cursor: not-allowed !important;
    }
    .form-disabled button[disabled] {
      background-color: #9ca3af !important;
      cursor: not-allowed !important;
    }
    .dark .form-disabled input[disabled] {
      background-color: #374151 !important;
      color: #6b7280 !important;
    }
    .dark .form-disabled button[disabled] {
      background-color: #6b7280 !important;
    }
  </style>

{% include 'footer.html' %}
